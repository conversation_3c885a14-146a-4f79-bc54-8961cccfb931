/**
 * GameFlex Posts Lambda Functions
 * Handles post creation, retrieval, and management
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { CognitoIdentityProviderClient, GetUserCommand } from '@aws-sdk/client-cognito-identity-provider';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, QueryCommand, UpdateCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';

// AWS clients
const cognitoClient = new CognitoIdentityProviderClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

// DynamoDB configuration
const dynamoClient = new DynamoDBClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

const docClient = DynamoDBDocumentClient.from(dynamoClient);

// S3 configuration
const MEDIA_BUCKET = process.env.S3_BUCKET_MEDIA || 'gameflex-media-development';

// Table names
const USERS_TABLE = 'Users';
const POSTS_TABLE = 'Posts';
const MEDIA_TABLE = 'Media';
const CHANNEL_MEMBERS_TABLE = 'ChannelMembers';

interface User {
    id: string;
    cognito_user_id: string;
    email: string;
    username: string;
    display_name?: string;
    avatar_url?: string;
}

interface Post {
    id: string;
    content: string;
    like_count: number;
    comment_count: number;
    view_count: number;
    created_at: Date;
    updated_at: Date;
    author_id: string;
    username: string;
    display_name?: string;
    avatar_url?: string;
    channel_id?: string;
    channel_name?: string;
    media_id?: string;
    filename?: string;
    extension?: string;
    media_type?: string;
    width?: number;
    height?: number;
    s3_bucket?: string;
    s3_key?: string;
}

function createCorsResponse(statusCode: number, body: any): APIGatewayProxyResult {
    return {
        statusCode,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Amz-Date, X-Api-Key, X-Amz-Security-Token'
        },
        body: JSON.stringify(body)
    };
}

async function getUserFromToken(accessToken: string): Promise<User | null> {
    try {
        // Get user info from Cognito
        const getUserCommand = new GetUserCommand({
            AccessToken: accessToken
        });

        const response = await cognitoClient.send(getUserCommand);
        const cognitoUserId = response.Username!;

        // Get user from database
        const result = await docClient.send(new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'cognito_user_id-index',
            KeyConditionExpression: 'cognito_user_id = :cognitoUserId',
            FilterExpression: 'is_active = :isActive',
            ExpressionAttributeValues: {
                ':cognitoUserId': cognitoUserId,
                ':isActive': true
            }
        }));

        return result.Items && result.Items.length > 0 ? result.Items[0] as User : null;
    } catch (error) {
        console.error('Failed to get user from token:', error);
        return null;
    }
}

async function getPostsHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        console.log('Posts handler called with event:', JSON.stringify(event, null, 2));

        const queryParams = event.queryStringParameters || {};
        const limit = Math.min(parseInt(queryParams.limit || '20'), 100);
        const offset = parseInt(queryParams.offset || '0');

        console.log('Getting posts with params:', { limit, offset });

        // Return some hardcoded sample posts for now to test the connection
        const samplePosts = [
            {
                id: 'sample-1',
                user_id: 'user-1',
                author_id: 'user-1',
                channel_id: null,
                content: 'Welcome to GameFlex! 🎮 Ready to share your gaming moments?',
                media_url: null,
                media_type: 'image',
                media_id: null,
                like_count: 15,
                comment_count: 3,
                is_active: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                username: 'GameFlexUser',
                display_name: 'GameFlex User',
                avatar_url: null,
                is_liked_by_current_user: false
            },
            {
                id: 'sample-2',
                user_id: 'user-2',
                author_id: 'user-2',
                channel_id: null,
                content: 'Just hit a new high score! Who else is playing this weekend? 🏆',
                media_url: null,
                media_type: 'image',
                media_id: null,
                like_count: 28,
                comment_count: 7,
                is_active: true,
                created_at: new Date(Date.now() - 60000).toISOString(),
                updated_at: new Date(Date.now() - 60000).toISOString(),
                username: 'ProGamer',
                display_name: 'Pro Gamer',
                avatar_url: null,
                is_liked_by_current_user: false
            },
            {
                id: 'sample-3',
                user_id: 'user-3',
                author_id: 'user-3',
                channel_id: null,
                content: 'Epic gaming session last night! Check out this amazing play 🔥',
                media_url: null,
                media_type: 'video',
                media_id: null,
                like_count: 42,
                comment_count: 12,
                is_active: true,
                created_at: new Date(Date.now() - 120000).toISOString(),
                updated_at: new Date(Date.now() - 120000).toISOString(),
                username: 'EpicPlayer',
                display_name: 'Epic Player',
                avatar_url: null,
                is_liked_by_current_user: false
            }
        ];

        console.log(`Returning ${samplePosts.length} sample posts`);

        return createCorsResponse(200, {
            posts: samplePosts,
            total: samplePosts.length,
            limit,
            offset
        });

    } catch (error) {
        console.error('Get posts handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error',
            details: error.message
        });
    }
}

async function createPostHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const authHeader = event.headers.Authorization || event.headers.authorization || '';

        if (!authHeader.startsWith('Bearer ')) {
            return createCorsResponse(401, {
                error: 'Authorization header required'
            });
        }

        const accessToken = authHeader.substring(7);

        // Get user from token
        const user = await getUserFromToken(accessToken);
        if (!user) {
            return createCorsResponse(401, {
                error: 'Invalid or expired token'
            });
        }

        console.log('Raw event body:', event.body);

        let body;
        try {
            // Handle potential double-encoding
            let bodyStr = event.body || '{}';
            if (typeof bodyStr === 'string' && bodyStr.startsWith('"') && bodyStr.endsWith('"')) {
                // If the body is double-encoded as a JSON string, parse it twice
                bodyStr = JSON.parse(bodyStr);
            }
            body = JSON.parse(bodyStr);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            console.error('Body content:', event.body);
            return createCorsResponse(400, {
                error: 'Invalid JSON in request body'
            });
        }

        const content = body.content?.trim();
        const channelId = body.channel_id;
        const mediaId = body.media_id;

        if (!content) {
            return createCorsResponse(400, {
                error: 'Content is required'
            });
        }

        // Validate channel membership if channel_id provided
        if (channelId) {
            const result = await docClient.send(new GetCommand({
                TableName: CHANNEL_MEMBERS_TABLE,
                Key: {
                    channel_id: channelId,
                    user_id: user.id
                }
            }));

            if (!result.Item) {
                return createCorsResponse(403, {
                    error: 'You are not a member of this channel'
                });
            }
        }

        // TODO: Implement DynamoDB post creation
        const postId = uuidv4();
        const now = new Date().toISOString();

        const post = {
            id: postId,
            content: content,
            media_id: mediaId,
            author_id: user.id,
            channel_id: channelId,
            like_count: 0,
            comment_count: 0,
            view_count: 0,
            created_at: now,
            updated_at: now
        };

        await docClient.send(new PutCommand({
            TableName: POSTS_TABLE,
            Item: post
        }));

        return createCorsResponse(201, {
            message: 'Post created successfully',
            post: {
                id: post.id,
                content: post.content,
                like_count: post.like_count,
                comment_count: post.comment_count,
                view_count: post.view_count,
                created_at: post.created_at,
                updated_at: post.updated_at,
                author: {
                    id: user.id,
                    username: user.username,
                    display_name: user.display_name,
                    avatar_url: user.avatar_url
                }
            }
        });

    } catch (error) {
        console.error('Create post handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function getPostHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const postId = event.pathParameters?.id;

        if (!postId) {
            return createCorsResponse(400, {
                error: 'Post ID is required'
            });
        }

        // TODO: Implement DynamoDB post retrieval with joins
        const result = await docClient.send(new GetCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId }
        }));

        if (!result.Item) {
            return createCorsResponse(404, {
                error: 'Post not found'
            });
        }

        const post = result.Item;

        // TODO: Increment view count in DynamoDB

        // Return simplified post data
        return createCorsResponse(200, {
            post: {
                id: post.id,
                content: post.content,
                like_count: post.like_count || 0,
                comment_count: post.comment_count || 0,
                view_count: (post.view_count || 0) + 1,
                created_at: post.created_at,
                updated_at: post.updated_at,
                author_id: post.author_id,
                channel_id: post.channel_id,
                media_id: post.media_id
            }
        });

    } catch (error) {
        console.error('Get post handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createCorsResponse(200, {});
    }

    const path = event.path;
    const method = event.httpMethod;

    try {
        if (path === '/posts' && method === 'GET') {
            return await getPostsHandler(event);
        } else if (path === '/posts' && method === 'POST') {
            return await createPostHandler(event);
        } else if (path.startsWith('/posts/') && method === 'GET') {
            return await getPostHandler(event);
        } else {
            return createCorsResponse(404, {
                error: 'Endpoint not found'
            });
        }
    } catch (error) {
        console.error('Lambda handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
};
