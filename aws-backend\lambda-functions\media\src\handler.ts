/**
 * GameFlex Media Lambda Functions
 * Handles media upload, processing, and management
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { CognitoIdentityProviderClient, GetUserCommand } from '@aws-sdk/client-cognito-identity-provider';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';

// AWS clients
const s3Client = new S3Client({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    },
    forcePathStyle: true
});

const cognitoClient = new CognitoIdentityProviderClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

// DynamoDB configuration
const dynamoClient = new DynamoDBClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

const docClient = DynamoDBDocumentClient.from(dynamoClient);

// S3 configuration
const MEDIA_BUCKET = process.env.S3_BUCKET_MEDIA || 'gameflex-media-development';
const AVATARS_BUCKET = process.env.S3_BUCKET_AVATARS || 'gameflex-avatars-development';
const TEMP_BUCKET = process.env.S3_BUCKET_TEMP || 'gameflex-temp-development';

// Table names
const USERS_TABLE = 'Users';
const MEDIA_TABLE = 'Media';
const CHANNEL_MEMBERS_TABLE = 'ChannelMembers';

// File configuration
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '52428800'); // 50MB
const ALLOWED_IMAGE_TYPES = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
const ALLOWED_VIDEO_TYPES = ['mp4', 'mov', 'avi', 'webm'];

interface User {
    id: string;
    cognito_user_id: string;
    email: string;
    username: string;
    display_name?: string;
    avatar_url?: string;
}

interface ProcessedImage {
    data: Buffer;
    width: number;
    height: number;
    size: number;
}

function createCorsResponse(statusCode: number, body: any): APIGatewayProxyResult {
    return {
        statusCode,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Amz-Date, X-Api-Key, X-Amz-Security-Token'
        },
        body: JSON.stringify(body)
    };
}

async function getUserFromToken(accessToken: string): Promise<User | null> {
    try {
        // Get user info from Cognito
        const getUserCommand = new GetUserCommand({
            AccessToken: accessToken
        });

        const response = await cognitoClient.send(getUserCommand);
        const cognitoUserId = response.Username!;

        // Get user from database
        const result = await docClient.send(new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'cognito_user_id-index',
            KeyConditionExpression: 'cognito_user_id = :cognitoUserId',
            FilterExpression: 'is_active = :isActive',
            ExpressionAttributeValues: {
                ':cognitoUserId': cognitoUserId,
                ':isActive': true
            }
        }));

        return result.Items && result.Items.length > 0 ? result.Items[0] as User : null;
    } catch (error) {
        console.error('Failed to get user from token:', error);
        return null;
    }
}

function getFileType(filename: string): string {
    const extension = filename.toLowerCase().split('.').pop() || '';

    if (ALLOWED_IMAGE_TYPES.includes(extension)) {
        return 'image';
    } else if (ALLOWED_VIDEO_TYPES.includes(extension)) {
        return 'video';
    } else {
        return 'unknown';
    }
}

function getMimeType(extension: string): string {
    const mimeTypes: { [key: string]: string } = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'webp': 'image/webp',
        'mp4': 'video/mp4',
        'mov': 'video/quicktime',
        'avi': 'video/x-msvideo',
        'webm': 'video/webm'
    };
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}

async function processImage(imageData: Buffer): Promise<ProcessedImage> {
    try {
        const image = sharp(imageData);
        const metadata = await image.metadata();

        let { width = 0, height = 0 } = metadata;

        // Resize if too large (max 2048x2048)
        const maxSize = 2048;
        if (width > maxSize || height > maxSize) {
            const resized = await image
                .resize(maxSize, maxSize, {
                    fit: 'inside',
                    withoutEnlargement: true
                })
                .jpeg({ quality: 85 })
                .toBuffer();

            const resizedMetadata = await sharp(resized).metadata();
            width = resizedMetadata.width || 0;
            height = resizedMetadata.height || 0;

            return {
                data: resized,
                width,
                height,
                size: resized.length
            };
        } else {
            // Convert to JPEG for consistency
            const processed = await image
                .jpeg({ quality: 85 })
                .toBuffer();

            return {
                data: processed,
                width,
                height,
                size: processed.length
            };
        }
    } catch (error) {
        console.error('Image processing failed:', error);
        throw new Error('Image processing failed');
    }
}

async function uploadMediaHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const authHeader = event.headers.Authorization || event.headers.authorization || '';

        if (!authHeader.startsWith('Bearer ')) {
            return createCorsResponse(401, {
                error: 'Authorization header required'
            });
        }

        const accessToken = authHeader.substring(7);

        // Get user from token
        const user = await getUserFromToken(accessToken);
        if (!user) {
            return createCorsResponse(401, {
                error: 'Invalid or expired token'
            });
        }

        const body = JSON.parse(event.body || '{}');
        const filename = body.filename || '';
        const fileDataB64 = body.file_data || '';
        const channelId = body.channel_id;

        if (!filename || !fileDataB64) {
            return createCorsResponse(400, {
                error: 'Filename and file data are required'
            });
        }

        // Decode file data
        let fileData: Buffer;
        try {
            fileData = Buffer.from(fileDataB64, 'base64');
        } catch (error) {
            return createCorsResponse(400, {
                error: 'Invalid file data encoding'
            });
        }

        // Check file size
        if (fileData.length > MAX_FILE_SIZE) {
            return createCorsResponse(400, {
                error: `File size exceeds maximum allowed size of ${MAX_FILE_SIZE} bytes`
            });
        }

        // Get file extension and type
        const extension = filename.toLowerCase().split('.').pop() || '';
        const fileType = getFileType(filename);

        if (fileType === 'unknown') {
            return createCorsResponse(400, {
                error: 'Unsupported file type'
            });
        }

        // Validate channel membership if channel_id provided
        if (channelId) {
            const result = await docClient.send(new GetCommand({
                TableName: CHANNEL_MEMBERS_TABLE,
                Key: {
                    channel_id: channelId,
                    user_id: user.id
                }
            }));

            if (!result.Item) {
                return createCorsResponse(403, {
                    error: 'You are not a member of this channel'
                });
            }
        }

        // Process file based on type
        let processedData = fileData;
        let width: number | undefined;
        let height: number | undefined;

        if (fileType === 'image') {
            try {
                const processed = await processImage(fileData);
                processedData = processed.data;
                width = processed.width;
                height = processed.height;
            } catch (error) {
                return createCorsResponse(400, {
                    error: `Image processing failed: ${error}`
                });
            }
        }

        // Generate unique filename
        const mediaId = uuidv4();
        const timestamp = Date.now();
        const uniqueFilename = `${mediaId}_${timestamp}.${extension}`;

        // Create S3 key
        const s3Key = channelId
            ? `user/${user.id}/${channelId}/${uniqueFilename}`
            : `user/${user.id}/${uniqueFilename}`;

        // Upload to S3
        try {
            const putObjectCommand = new PutObjectCommand({
                Bucket: MEDIA_BUCKET,
                Key: s3Key,
                Body: processedData,
                ContentType: getMimeType(extension),
                Metadata: {
                    'original_filename': filename,
                    'uploaded_by': user.id,
                    'upload_timestamp': timestamp.toString()
                }
            });

            await s3Client.send(putObjectCommand);
        } catch (error) {
            console.error('S3 upload failed:', error);
            return createCorsResponse(500, {
                error: 'Failed to upload file'
            });
        }

        // Create media record in database
        const now = new Date().toISOString();
        const media = {
            id: mediaId,
            filename: uniqueFilename.split('.')[0],
            original_filename: filename,
            extension: extension,
            mime_type: getMimeType(extension),
            size_bytes: processedData.length,
            width: width,
            height: height,
            type: fileType,
            s3_bucket: MEDIA_BUCKET,
            s3_key: s3Key,
            location: 'user',
            owner_id: user.id,
            channel_id: channelId,
            is_processed: true,
            processing_status: 'completed',
            created_at: now,
            updated_at: now
        };

        await docClient.send(new PutCommand({
            TableName: MEDIA_TABLE,
            Item: media
        }));

        // Generate media URL
        const mediaUrl = `http://127.0.0.1:45660/${MEDIA_BUCKET}/${s3Key}`;

        return createCorsResponse(201, {
            message: 'Media uploaded successfully',
            media: {
                id: media.id,
                filename: media.filename,
                extension: media.extension,
                type: media.type,
                width: media.width,
                height: media.height,
                url: mediaUrl,
                created_at: media.created_at
            }
        });

    } catch (error) {
        console.error('Upload media handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function getMediaHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const mediaId = event.pathParameters?.id;

        if (!mediaId) {
            return createCorsResponse(400, {
                error: 'Media ID is required'
            });
        }

        // Get media record
        const mediaResult = await docClient.send(new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id: mediaId }
        }));

        if (!mediaResult.Item) {
            return createCorsResponse(404, {
                error: 'Media not found'
            });
        }

        const media = mediaResult.Item;

        // Get owner information
        const ownerResult = await docClient.send(new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: media.owner_id }
        }));

        const owner = ownerResult.Item;

        // Generate media URL
        const mediaUrl = `http://127.0.0.1:45660/${media.s3_bucket}/${media.s3_key}`;

        return createCorsResponse(200, {
            media: {
                id: media.id,
                filename: media.filename,
                original_filename: media.original_filename,
                extension: media.extension,
                mime_type: media.mime_type,
                size_bytes: media.size_bytes,
                width: media.width,
                height: media.height,
                type: media.type,
                url: mediaUrl,
                is_processed: media.is_processed,
                processing_status: media.processing_status,
                created_at: media.created_at,
                owner: {
                    id: media.owner_id,
                    username: owner?.username || 'Unknown',
                    display_name: owner?.display_name || 'Unknown User'
                }
            }
        });

    } catch (error) {
        console.error('Get media handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createCorsResponse(200, {});
    }

    const path = event.path;
    const method = event.httpMethod;

    try {
        if (path === '/media/upload' && method === 'POST') {
            return await uploadMediaHandler(event);
        } else if (path.startsWith('/media/') && method === 'GET') {
            return await getMediaHandler(event);
        } else {
            return createCorsResponse(404, {
                error: 'Endpoint not found'
            });
        }
    } catch (error) {
        console.error('Lambda handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
};
