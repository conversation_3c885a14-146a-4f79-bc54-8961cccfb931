import 'dart:io';
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
// TODO: Implement AWS upload service

class PostCompositionScreen extends StatefulWidget {
  final File croppedImageFile;

  const PostCompositionScreen({super.key, required this.croppedImageFile});

  @override
  State<PostCompositionScreen> createState() => _PostCompositionScreenState();
}

class _PostCompositionScreenState extends State<PostCompositionScreen> {
  final TextEditingController _textController = TextEditingController();
  bool _isPosting = false;
  final int _maxCharacters = 500;

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  Future<void> _submitPost() async {
    if (_isPosting) return;

    setState(() {
      _isPosting = true;
    });

    try {
      // TODO: Implement AWS upload service
      final success = false;

      if (success && mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Post created successfully!'),
            backgroundColor: AppColors.gfGreen,
          ),
        );

        // Navigate back to home screen (pop all the way back)
        Navigator.of(context).popUntil((route) => route.isFirst);
      } else {
        _showErrorSnackBar('Failed to create post. Please try again.');
      }
    } catch (e) {
      // TODO: Add proper logging for AWS upload service error
      _showErrorSnackBar('An error occurred while creating your post.');
    } finally {
      if (mounted) {
        setState(() {
          _isPosting = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final remainingCharacters = _maxCharacters - _textController.text.length;

    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      appBar: AppBar(
        title: const Text(
          'Create Post',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.darkBlue,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
        elevation: 0,
        actions: [
          if (!_isPosting)
            TextButton(
              onPressed:
                  _textController.text.trim().isNotEmpty ? _submitPost : null,
              child: Text(
                'Post',
                style: TextStyle(
                  color:
                      _textController.text.trim().isNotEmpty
                          ? AppColors.gfGreen
                          : AppColors.gfGrayText,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image preview
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.gfGrayBorder),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.file(widget.croppedImageFile, fit: BoxFit.cover),
                ),
              ),

              const SizedBox(height: 24),

              // Text input label
              const Text(
                'Add a caption',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gfOffWhite,
                ),
              ),

              const SizedBox(height: 12),

              // Text input field
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.gfCardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.gfGrayBorder),
                  ),
                  child: TextField(
                    controller: _textController,
                    maxLines: null,
                    maxLength: _maxCharacters,
                    style: const TextStyle(
                      color: AppColors.gfOffWhite,
                      fontSize: 16,
                    ),
                    decoration: const InputDecoration(
                      hintText: 'What\'s on your mind?',
                      hintStyle: TextStyle(
                        color: AppColors.gfGrayText,
                        fontSize: 16,
                      ),
                      border: InputBorder.none,
                      counterText: '', // Hide the built-in counter
                    ),
                    onChanged: (text) {
                      setState(
                        () {},
                      ); // Rebuild to update character count and button state
                    },
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // Character counter
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$remainingCharacters characters remaining',
                    style: TextStyle(
                      color:
                          remainingCharacters < 50
                              ? Colors.orange
                              : AppColors.gfGrayText,
                      fontSize: 14,
                    ),
                  ),
                  if (_isPosting)
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: AppColors.gfGreen,
                        strokeWidth: 2,
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 24),

              // Submit button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed:
                      _isPosting || _textController.text.trim().isEmpty
                          ? null
                          : _submitPost,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.gfGreen,
                    foregroundColor: AppColors.darkBlue,
                    disabledBackgroundColor: AppColors.gfGrayBorder,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child:
                      _isPosting
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: AppColors.darkBlue,
                              strokeWidth: 2,
                            ),
                          )
                          : const Text(
                            'Create Post',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
