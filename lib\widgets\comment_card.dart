import 'package:flutter/material.dart';
import '../models/comment_model.dart';
import '../theme/app_theme.dart';
import '../services/aws_auth_service.dart';

class CommentCard extends StatelessWidget {
  final CommentModel comment;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const CommentCard({
    super.key,
    required this.comment,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final currentUser = AwsAuthService.instance.currentUser;
    final isOwnComment = currentUser?.id == comment.userId;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: AppColors.gfCardBackground,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: AppColors.gfGrayBorder.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with user info and timestamp
          Row(
            children: [
              // Avatar
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: const LinearGradient(
                    colors: [AppColors.gfLightTeal, AppColors.gfTeal],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child:
                    comment.avatarUrl != null && comment.avatarUrl!.isNotEmpty
                        ? ClipOval(
                          child: Image.network(
                            comment.avatarUrl!,
                            width: 32,
                            height: 32,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.person,
                                size: 18,
                                color: AppColors.gfDarkBlue,
                              );
                            },
                          ),
                        )
                        : const Icon(
                          Icons.person,
                          size: 18,
                          color: AppColors.gfDarkBlue,
                        ),
              ),
              const SizedBox(width: 8),

              // User info and timestamp
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      comment.authorDisplayName,
                      style: const TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          '@${comment.authorUsername}',
                          style: const TextStyle(
                            color: AppColors.gfGrayText,
                            fontSize: 12,
                          ),
                        ),
                        const Text(
                          ' • ',
                          style: TextStyle(
                            color: AppColors.gfGrayText,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          comment.timeAgo,
                          style: const TextStyle(
                            color: AppColors.gfGrayText,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Options menu (only for own comments)
              if (isOwnComment)
                PopupMenuButton<String>(
                  icon: const Icon(
                    Icons.more_vert,
                    color: AppColors.gfGrayText,
                    size: 18,
                  ),
                  color: AppColors.gfDarkBackground,
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        onEdit?.call();
                        break;
                      case 'delete':
                        _showDeleteDialog(context);
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(
                                Icons.edit,
                                color: AppColors.gfOffWhite,
                                size: 18,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Edit',
                                style: TextStyle(color: AppColors.gfOffWhite),
                              ),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red, size: 18),
                              SizedBox(width: 8),
                              Text(
                                'Delete',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                ),
            ],
          ),

          const SizedBox(height: 8),

          // Comment content
          Text(
            comment.content,
            style: const TextStyle(
              color: AppColors.gfOffWhite,
              fontSize: 14,
              height: 1.4,
            ),
          ),

          const SizedBox(height: 8),

          // Action buttons (like, reply - for future implementation)
          Row(
            children: [
              _buildActionButton(
                icon: Icons.shield_outlined,
                label:
                    comment.likeCount > 0 ? comment.likeCount.toString() : '',
                onPressed: () {
                  // TODO: Implement comment liking
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Comment liking coming soon!'),
                      backgroundColor: AppColors.gfTeal,
                    ),
                  );
                },
              ),
              const SizedBox(width: 16),
              _buildActionButton(
                icon: Icons.reply,
                label: 'Reply',
                onPressed: () {
                  // TODO: Implement comment replies
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Comment replies coming soon!'),
                      backgroundColor: AppColors.gfTeal,
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(16),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: AppColors.gfGrayText),
            if (label.isNotEmpty) ...[
              const SizedBox(width: 4),
              Text(
                label,
                style: const TextStyle(
                  color: AppColors.gfGrayText,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.gfDarkBackground,
            title: const Text(
              'Delete Comment',
              style: TextStyle(color: AppColors.gfOffWhite),
            ),
            content: const Text(
              'Are you sure you want to delete this comment? This action cannot be undone.',
              style: TextStyle(color: AppColors.gfGrayText),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: AppColors.gfGrayText),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onDelete?.call();
                },
                child: const Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }
}
