import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import '../models/post_model.dart';
import 'api_service.dart';
import 'aws_auth_service.dart';

/// AWS posts service for managing posts via API Gateway
class AwsPostsService {
  static AwsPostsService? _instance;
  static AwsPostsService get instance => _instance ??= AwsPostsService._();

  AwsPostsService._();

  /// Get posts with pagination
  Future<List<PostModel>> getPosts({
    int limit = 20,
    int offset = 0,
    String? channelId,
  }) async {
    try {
      developer.log(
        'AwsPostsService: Getting posts (limit: $limit, offset: $offset)',
      );

      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      if (channelId != null) {
        queryParams['channel_id'] = channelId;
      }

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final path = '/posts${queryString.isNotEmpty ? '?$queryString' : ''}';

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: path,
      );

      final data = ApiService.instance.parseResponse(response);
      final postsData = data['posts'] as List<dynamic>? ?? [];

      final posts =
          postsData
              .map(
                (postData) =>
                    _convertToPostModel(postData as Map<String, dynamic>),
              )
              .toList();

      developer.log('AwsPostsService: Retrieved ${posts.length} posts');
      return posts;
    } catch (e) {
      developer.log('AwsPostsService: Error getting posts: $e');
      if (kDebugMode) {
        print('AwsPostsService ERROR: $e');
      }
      throw Exception('Failed to get posts: $e');
    }
  }

  /// Get a specific post by ID
  Future<PostModel?> getPost(String postId) async {
    try {
      developer.log('AwsPostsService: Getting post $postId');

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/posts/$postId',
      );

      final data = ApiService.instance.parseResponse(response);
      final postData = data['post'] as Map<String, dynamic>?;

      if (postData != null) {
        final post = _convertToPostModel(postData);
        developer.log('AwsPostsService: Retrieved post $postId');
        return post;
      }

      return null;
    } catch (e) {
      developer.log('AwsPostsService: Error getting post $postId: $e');
      if (kDebugMode) {
        print('AwsPostsService ERROR: $e');
      }
      return null;
    }
  }

  /// Create a new post
  Future<PostModel?> createPost({
    required String content,
    String? channelId,
    String? mediaId,
  }) async {
    try {
      developer.log('AwsPostsService: Creating post');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final body = <String, dynamic>{'content': content};

      if (channelId != null) {
        body['channel_id'] = channelId;
      }

      if (mediaId != null) {
        body['media_id'] = mediaId;
      }

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/posts',
        body: body,
        accessToken: accessToken,
      );

      final data = ApiService.instance.parseResponse(response);
      final postData = data['post'] as Map<String, dynamic>?;

      if (postData != null) {
        final post = _convertToPostModel(postData);
        developer.log('AwsPostsService: Created post ${post.id}');
        return post;
      }

      return null;
    } catch (e) {
      developer.log('AwsPostsService: Error creating post: $e');
      if (kDebugMode) {
        print('AwsPostsService ERROR: $e');
      }
      throw Exception('Failed to create post: $e');
    }
  }

  /// Like a post
  Future<bool> likePost(String postId) async {
    try {
      developer.log('AwsPostsService: Liking post $postId');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/posts/$postId/like',
        accessToken: accessToken,
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsPostsService: Liked post $postId');
      return true;
    } catch (e) {
      developer.log('AwsPostsService: Error liking post $postId: $e');
      if (kDebugMode) {
        print('AwsPostsService ERROR: $e');
      }
      return false;
    }
  }

  /// Unlike a post
  Future<bool> unlikePost(String postId) async {
    try {
      developer.log('AwsPostsService: Unliking post $postId');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'DELETE',
        path: '/posts/$postId/like',
        accessToken: accessToken,
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsPostsService: Unliked post $postId');
      return true;
    } catch (e) {
      developer.log('AwsPostsService: Error unliking post $postId: $e');
      if (kDebugMode) {
        print('AwsPostsService ERROR: $e');
      }
      return false;
    }
  }

  /// Convert AWS API response to PostModel
  PostModel _convertToPostModel(Map<String, dynamic> data) {
    // Extract media information if available
    String? mediaUrl;
    String? mediaType;

    if (data['s3_bucket'] != null && data['s3_key'] != null) {
      // Construct media URL from S3 information
      final bucket = data['s3_bucket'] as String;
      final key = data['s3_key'] as String;
      mediaUrl = _constructMediaUrl(bucket, key);
      mediaType = data['media_type'] as String?;
    }

    return PostModel(
      id: data['id'] as String,
      userId: (data['author_id'] ?? data['user_id']) as String,
      channelId: data['channel_id'] as String?,
      content: data['content'] as String? ?? '',
      mediaId: data['media_id'] as String?,
      mediaUrl: mediaUrl,
      mediaType: mediaType,
      likeCount: data['like_count'] as int? ?? 0,
      commentCount: data['comment_count'] as int? ?? 0,
      isActive: true, // AWS API only returns active posts
      createdAt: DateTime.parse(data['created_at'] as String),
      updatedAt: DateTime.parse(data['updated_at'] as String),
      // User information
      username: data['username'] as String? ?? 'Unknown',
      displayName: data['display_name'] as String?,
      avatarUrl: data['avatar_url'] as String?,
    );
  }

  /// Construct media URL from S3 bucket and key
  String _constructMediaUrl(String bucket, String key) {
    // For LocalStack development, construct URL to LocalStack S3
    // TODO: Make this environment-aware for production
    return 'http://localhost:45660/$bucket/$key';
  }
}
