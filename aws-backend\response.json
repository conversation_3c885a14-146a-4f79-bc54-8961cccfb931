{"errorType": "Runtime.ImportModuleError", "errorMessage": "Error: Cannot find module 'index'\nRequire stack:\n- /var/runtime/index.mjs", "trace": ["Runtime.ImportModuleError: Error: Cannot find module 'index'", "Require stack:", "- /var/runtime/index.mjs", "    at _loadUserApp (file:///var/runtime/index.mjs:1109:17)", "    at async UserFunction.js.module.exports.load (file:///var/runtime/index.mjs:1148:21)", "    at async start (file:///var/runtime/index.mjs:1335:23)", "    at async file:///var/runtime/index.mjs:1342:1"]}