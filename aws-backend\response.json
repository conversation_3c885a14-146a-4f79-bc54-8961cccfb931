{"statusCode": 200, "headers": {"Content-Type": "application/json", "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS", "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Amz-Date, X-Api-Key, X-Amz-Security-Token"}, "body": "{\"message\":\"Authentication successful\",\"tokens\":{\"access_token\":\"********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"refresh_token\":\"856e301e\",\"id_token\":\"eyJhbGciOiJSUzI1NiIsImtpZCI6IjRkNzMyZGMyLTdjM2MtNGQ1YS05YTMyLWE3MDNhNWNjYzNjMSIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Z5AnrCiquQeoOVRg_ZeYTP7ls6mvBdRK3Lyomm2ByGzv2r_DGGcHGPDMxpzuB0D4x18KoqEUFsIPe65CGwvgIsZLgAsGYNEjm2HM0Si3_oa1S-G6nTNoHZ-cvJ8wdcOsiMELgnCnSrpI4206vxk0eVU7TcrfJA1dvPcMUose8sK2VHT8dNpBHogO4Pw3BJox2QH9l9Roes9s2mEIYSdVDkwHz-eKkafUvj7BeBCpY2EdYjWG3psm6iNC9yz6WPbTwMWgFbSAO_kz_JuV2FMtobr5sgxqtUlSqwDc-AR0iY_5GPm5maE1JxyJdhd1y6R4y-8difVROz4m7SfTUES6MQ\"},\"user\":{\"id\":\"550e8400-e29b-41d4-a716-446655440001\",\"email\":\"<EMAIL>\",\"username\":\"developer\",\"display_name\":\"GameFlex Developer\",\"is_verified\":true}}"}