#!/usr/bin/env node

/**
 * Seed sample posts for testing the posts scrolling functionality
 */

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand, ScanCommand } = require('@aws-sdk/lib-dynamodb');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

// AWS clients configuration for LocalStack
const awsConfig = {
    endpoint: 'http://localhost:45660',
    region: 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
};

const dynamoClient = new DynamoDBClient(awsConfig);
const s3Client = new S3Client(awsConfig);
const docClient = DynamoDBDocumentClient.from(dynamoClient);

const USERS_TABLE = 'Users';
const POSTS_TABLE = 'Posts';
const MEDIA_BUCKET = 'gameflex-media-development';

// Sample post content with media files
const samplePosts = [
    {
        content: "Just had an epic Call of Duty session! Check out this clutch moment 🎮🔥",
        media_type: "image",
        media_file: "cod_screenshot.jpg"
    },
    {
        content: "Diablo 4 is absolutely insane! This boss fight was legendary 🔥⚔️",
        media_type: "image",
        media_file: "diablo_screenshot.webp"
    }
];

async function uploadMediaFile(filename) {
    try {
        const filePath = path.join(__dirname, '..', 'assets', 'media', filename);

        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  Media file not found: ${filePath}`);
            return null;
        }

        const fileContent = fs.readFileSync(filePath);
        const contentType = filename.endsWith('.jpg') || filename.endsWith('.jpeg') ? 'image/jpeg' :
            filename.endsWith('.webp') ? 'image/webp' :
                filename.endsWith('.png') ? 'image/png' : 'application/octet-stream';

        await s3Client.send(new PutObjectCommand({
            Bucket: MEDIA_BUCKET,
            Key: filename,
            Body: fileContent,
            ContentType: contentType
        }));

        console.log(`✅ Uploaded ${filename} to S3`);
        return filename;
    } catch (error) {
        console.error(`❌ Error uploading ${filename}:`, error);
        return null;
    }
}

async function getUsers() {
    try {
        const result = await docClient.send(new ScanCommand({
            TableName: USERS_TABLE
        }));
        return result.Items || [];
    } catch (error) {
        console.error('Error getting users:', error);
        return [];
    }
}

async function createSamplePosts() {
    try {
        console.log('🌱 Starting to seed sample posts...');

        // Get existing users
        const users = await getUsers();
        if (users.length === 0) {
            console.log('❌ No users found. Please create users first.');
            return;
        }

        console.log(`📋 Found ${users.length} users`);

        // Upload media files first
        console.log('📤 Uploading media files to S3...');
        const mediaFiles = [];
        for (const samplePost of samplePosts) {
            if (samplePost.media_file) {
                const uploadedFile = await uploadMediaFile(samplePost.media_file);
                if (uploadedFile) {
                    mediaFiles.push(uploadedFile);
                }
            }
        }

        // Create posts
        const now = new Date().toISOString();
        let createdCount = 0;

        for (let i = 0; i < samplePosts.length; i++) {
            const samplePost = samplePosts[i];
            const randomUser = users[Math.floor(Math.random() * users.length)];

            // Create post with timestamp slightly in the past for variety
            const postTime = new Date(Date.now() - (i * 60000)).toISOString(); // 1 minute apart

            const post = {
                id: uuidv4(),
                content: samplePost.content,
                author_id: randomUser.id,
                channel_id: null, // No channel for general feed
                media_id: samplePost.media_file || null,
                media_type: samplePost.media_type,
                s3_bucket: samplePost.media_file ? MEDIA_BUCKET : null,
                s3_key: samplePost.media_file || null,
                like_count: Math.floor(Math.random() * 50) + 10, // Random likes 10-59
                comment_count: Math.floor(Math.random() * 20) + 1, // Random comments 1-20
                view_count: Math.floor(Math.random() * 100) + 20, // Random views 20-119
                created_at: postTime,
                updated_at: postTime
            };

            await docClient.send(new PutCommand({
                TableName: POSTS_TABLE,
                Item: post
            }));

            createdCount++;
            console.log(`✅ Created post ${createdCount}/${samplePosts.length}: "${post.content.substring(0, 50)}..." ${post.media_id ? `with media: ${post.media_id}` : ''}`);
        }

        console.log(`🎉 Successfully created ${createdCount} sample posts with media!`);

    } catch (error) {
        console.error('❌ Error creating sample posts:', error);
    }
}

// Run the seeding
createSamplePosts().then(() => {
    console.log('🏁 Seeding completed!');
    process.exit(0);
}).catch((error) => {
    console.error('💥 Seeding failed:', error);
    process.exit(1);
});
