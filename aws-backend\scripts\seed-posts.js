#!/usr/bin/env node

/**
 * Seed sample posts for testing the posts scrolling functionality
 */

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand, ScanCommand } = require('@aws-sdk/lib-dynamodb');
const { v4: uuidv4 } = require('uuid');

// DynamoDB configuration for LocalStack
const dynamoClient = new DynamoDBClient({
    endpoint: 'http://localhost:45660',
    region: 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

const docClient = DynamoDBDocumentClient.from(dynamoClient);

const USERS_TABLE = 'Users';
const POSTS_TABLE = 'Posts';

// Sample post content
const samplePosts = [
    {
        content: "Welcome to GameFlex! 🎮 Ready to share your gaming moments?",
        media_type: "image"
    },
    {
        content: "Just hit a new high score! Who else is playing this weekend? 🏆",
        media_type: "image"
    },
    {
        content: "Epic gaming session last night! Check out this amazing play 🔥",
        media_type: "video"
    },
    {
        content: "New game just dropped! Anyone else excited to try it? 🎯",
        media_type: "image"
    },
    {
        content: "Gaming setup complete! What do you think? ⚡",
        media_type: "image"
    },
    {
        content: "Streaming live now! Come join the fun 🎬",
        media_type: "video"
    },
    {
        content: "Best gaming moment of the week! Had to share this 🌟",
        media_type: "video"
    },
    {
        content: "Who's up for some multiplayer action tonight? 🎮",
        media_type: "image"
    },
    {
        content: "Just discovered this hidden easter egg! Mind blown 🤯",
        media_type: "image"
    },
    {
        content: "Gaming marathon weekend! What's everyone playing? 🎲",
        media_type: "video"
    },
    {
        content: "New controller arrived! Time to level up my game 🎮",
        media_type: "image"
    },
    {
        content: "Epic boss battle! This took me 20 tries but finally got it 💪",
        media_type: "video"
    },
    {
        content: "Retro gaming night! Bringing back the classics 🕹️",
        media_type: "image"
    },
    {
        content: "Speed run attempt! Let's see if I can beat my record ⚡",
        media_type: "video"
    },
    {
        content: "Gaming with friends is the best! Squad goals 👥",
        media_type: "image"
    }
];

async function getUsers() {
    try {
        const result = await docClient.send(new ScanCommand({
            TableName: USERS_TABLE
        }));
        return result.Items || [];
    } catch (error) {
        console.error('Error getting users:', error);
        return [];
    }
}

async function createSamplePosts() {
    try {
        console.log('🌱 Starting to seed sample posts...');
        
        // Get existing users
        const users = await getUsers();
        if (users.length === 0) {
            console.log('❌ No users found. Please create users first.');
            return;
        }
        
        console.log(`📋 Found ${users.length} users`);
        
        // Create posts
        const now = new Date().toISOString();
        let createdCount = 0;
        
        for (let i = 0; i < samplePosts.length; i++) {
            const samplePost = samplePosts[i];
            const randomUser = users[Math.floor(Math.random() * users.length)];
            
            // Create post with timestamp slightly in the past for variety
            const postTime = new Date(Date.now() - (i * 60000)).toISOString(); // 1 minute apart
            
            const post = {
                id: uuidv4(),
                content: samplePost.content,
                author_id: randomUser.id,
                channel_id: null, // No channel for general feed
                media_id: null, // No actual media files for now
                media_type: samplePost.media_type,
                like_count: Math.floor(Math.random() * 50), // Random likes 0-49
                comment_count: Math.floor(Math.random() * 20), // Random comments 0-19
                view_count: Math.floor(Math.random() * 100), // Random views 0-99
                created_at: postTime,
                updated_at: postTime
            };
            
            await docClient.send(new PutCommand({
                TableName: POSTS_TABLE,
                Item: post
            }));
            
            createdCount++;
            console.log(`✅ Created post ${createdCount}/${samplePosts.length}: "${post.content.substring(0, 50)}..."`);
        }
        
        console.log(`🎉 Successfully created ${createdCount} sample posts!`);
        
    } catch (error) {
        console.error('❌ Error creating sample posts:', error);
    }
}

// Run the seeding
createSamplePosts().then(() => {
    console.log('🏁 Seeding completed!');
    process.exit(0);
}).catch((error) => {
    console.error('💥 Seeding failed:', error);
    process.exit(1);
});
